//
//  LoadingOverlayView.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^LoadingRetryHandler)(void);

@interface LoadingOverlayView : UIView

// 重试回调
@property (nonatomic, copy, nullable) LoadingRetryHandler retryHandler;

// 显示loading状态
- (void)showLoading;

// 显示错误状态（超时）
- (void)showError:(NSString *)errorMessage;

// 隐藏loading视图
- (void)hideLoading;

@end

NS_ASSUME_NONNULL_END
