//
//  UserDefaultsManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UserDefaultsManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 配置数据缓存
- (nullable NSDictionary *)getAppConfigData;
- (void)setAppConfigData:(NSDictionary *)configData;
- (void)clearAppConfigData;

// 认证数据缓存
- (nullable NSDictionary *)getAuthData;
- (void)setAuthData:(NSDictionary *)authData;
- (void)clearAuthData;

// 策略数据缓存
- (nullable NSDictionary *)getStrategyData;
- (void)setStrategyData:(NSDictionary *)strategyData;
- (void)clearStrategyData;

// 通用存储方法
- (nullable id)getObjectForKey:(NSString *)key;
- (void)setObject:(nullable id)object forKey:(NSString *)key;
- (void)removeObjectForKey:(NSString *)key;

// 类型安全的存储方法
- (nullable NSString *)getStringForKey:(NSString *)key;
- (void)setString:(nullable NSString *)string forKey:(NSString *)key;
- (NSInteger)getIntegerForKey:(NSString *)key;
- (void)setInteger:(NSInteger)integer forKey:(NSString *)key;
- (BOOL)getBoolForKey:(NSString *)key;
- (void)setBool:(BOOL)value forKey:(NSString *)key;
- (double)getDoubleForKey:(NSString *)key;
- (void)setDouble:(double)value forKey:(NSString *)key;

// 数据过期检查
- (BOOL)isDataExpired:(NSString *)key maxAge:(NSTimeInterval)maxAge;
- (void)setDataTimestamp:(NSString *)key;

@end

NS_ASSUME_NONNULL_END
