//
//  InitializationManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "InitializationManager.h"
#import "ThirdPartySDKManager.h"
#import "NetworkManager.h"
#import "GlobalDataManager.h"
#import "APIClient.h"
#import "DeviceInfoManager.h"
#import "KeychainManager.h"
#import "Constants.h"

@interface InitializationManager ()

@property (nonatomic, assign) InitializationState currentState;
@property (nonatomic, strong) ThirdPartySDKManager *thirdPartySDKManager;
@property (nonatomic, strong) NetworkManager *networkManager;
@property (nonatomic, strong) GlobalDataManager *globalDataManager;
@property (nonatomic, strong) APIClient *apiClient;
//@property (nonatomic, strong) dispatch_queue_t initQueue;
@property (nonatomic, copy, nullable) InitializationCompletionHandler completionHandler;
@property (nonatomic, assign) BOOL isCancelled;
@property (nonatomic, assign) BOOL thirdPartySDKInitialized;

@end

@implementation InitializationManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static InitializationManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.currentState = InitializationStateIdle;
        self.thirdPartySDKManager = [ThirdPartySDKManager sharedInstance];
        self.networkManager = [NetworkManager sharedInstance];
        self.globalDataManager = [GlobalDataManager sharedInstance];
        self.apiClient = [APIClient sharedInstance];
//        self.initQueue = dispatch_queue_create([Constants initializationQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);
        self.isCancelled = NO;
        self.thirdPartySDKInitialized = NO;
    }
    return self;
}

#pragma mark - 状态属性

- (BOOL)isInitializing {
    return self.currentState != InitializationStateIdle &&
           self.currentState != InitializationStateCompleted &&
           self.currentState != InitializationStateFailed;
}

- (BOOL)isInitialized {
    return self.currentState == InitializationStateCompleted;
}

- (BOOL)isThirdPartySDKInitialized {
    return self.thirdPartySDKInitialized;
}

#pragma mark - 初始化方法

- (void)startInitializationWithCompletion:(InitializationCompletionHandler)completion {
    if (self.isInitializing) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"InitializationError"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"Initialization is in progress"}];
            completion(NO, error);
        }
        return;
    }
    
    if (self.isInitialized) {
        if (completion) {
            completion(YES, nil);
        }
        return;
    }
    
    self.completionHandler = completion;
    self.isCancelled = NO;
    
//    dispatch_async(self.initQueue, ^{
//        
//    });
    [self executeInitializationFlow];
}

- (void)retryInitialization {
    [self resetInitializationState];
    [self startInitializationWithCompletion:self.completionHandler];
}

- (void)cancelInitialization {
    self.isCancelled = YES;
    [self transitionToState:InitializationStateFailed withUserInfo:@{@"reason": @"User cancelled"}];
}

#pragma mark - 初始化流程

- (void)executeInitializationFlow {
    if (self.isCancelled) return;

    // 异步启动第三方SDK初始化（不阻塞主流程）
    [self initializeThirdPartySDKsAsync];

    // 主流程直接进入网络等待阶段
    [self transitionToState:InitializationStateWaitingNetwork withUserInfo:nil];
    [self waitForNetworkConnection];
}

- (void)initializeThirdPartySDKsAsync {
    // 异步初始化第三方SDK，不阻塞主初始化流程
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSLog(@"开始异步初始化第三方SDK");

        [self.thirdPartySDKManager initializeSDKsWithCompletion:^(BOOL success, NSError *error) {
            self.thirdPartySDKInitialized = success;

            if (success) {
                NSLog(@"第三方SDK初始化成功");
                // 可以在这里执行一些需要SDK初始化完成后的操作
                // 比如上报归因数据等
                [self.thirdPartySDKManager reportAttributionDataWithCompletion:^(BOOL reportSuccess) {
                    if (reportSuccess) {
                        NSLog(@"归因数据上报成功");
                    } else {
                        NSLog(@"归因数据上报失败");
                    }
                }];
            } else {
                NSLog(@"第三方SDK初始化失败: %@", error.localizedDescription);
                // 第三方SDK初始化失败不影响主流程，只记录日志
            }
        }];
    });
}

- (void)waitForNetworkConnection {
    // 简化网络检查，直接使用NetworkManager的同步检查
    if ([self.networkManager isNetworkReachable]) {
        // 步骤3: 加载应用配置
        [self transitionToState:InitializationStateLoadingConfig withUserInfo:nil];
        [self loadAppConfig];
    } else {
        // 异步等待网络连接
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            int attempts = 0;
            const int maxAttempts = 30; // 最多等待30秒

            while (attempts < maxAttempts && !self.isCancelled) {
                if ([self.networkManager isNetworkReachable]) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        if (!self.isCancelled) {
                            [self transitionToState:InitializationStateLoadingConfig withUserInfo:nil];
                            [self loadAppConfig];
                        }
                    });
                    return;
                }

                sleep(1);
                attempts++;
            }

            // 超时或取消
            dispatch_async(dispatch_get_main_queue(), ^{
                if (!self.isCancelled) {
                    NSError *error = [NSError errorWithDomain:@"NetworkError"
                                                         code:-1002
                                                     userInfo:@{NSLocalizedDescriptionKey: @"网络连接不可用"}];
                    [self handleInitializationError:error];
                }
            });
        });
    }
}

- (void)loadAppConfig {
    // 检查缓存
    if (self.globalDataManager.appConfigData) {
        // 有缓存，异步更新
        [self loadAppConfigFromNetwork:NO];
        // 继续下一步
        [self proceedToAuthentication];
    } else {
        // 无缓存，同步加载
        [self loadAppConfigFromNetwork:YES];
    }
}

- (void)loadAppConfigFromNetwork:(BOOL)isRequired {
    // 调用APIClient获取配置
    [self.apiClient getAppConfigWithCompletion:^(BOOL success, id _Nullable data, NSError * _Nullable error) {
        if (self.isCancelled) return;

        if (success && data) {
            // 保存配置数据
            [self.globalDataManager setAppConfigData:data];

            if (isRequired) {
                [self proceedToAuthentication];
            }
        } else {
            // 处理错误情况
            NSLog(@"获取应用配置失败: %@", error.localizedDescription);

            if (isRequired) {
                // 如果是必需的配置，则报告初始化失败
                [self transitionToState:InitializationStateFailed
                          withUserInfo:@{@"error": error ?: [NSError errorWithDomain:@"InitializationError"
                                                                               code:-1002
                                                                           userInfo:@{NSLocalizedDescriptionKey: @"Failed to get app configuration"}]}];

                if (self.completionHandler) {
                    self.completionHandler(NO, error);
                }
            }
        }
    }];
}

- (void)proceedToAuthentication {
    if (self.isCancelled) return;
    
    // 步骤4: 用户认证
    [self transitionToState:InitializationStateAuthenticating withUserInfo:nil];
    
    // 检查是否已登录
    if ([self.globalDataManager isLoggedIn]) {
        // 已登录，跳过认证
        [self proceedToLoadStrategy];
    } else {
        // 执行登录
        [self performAuthentication];
    }
}

- (void)performAuthentication {
    // 获取设备ID用于登录
    NSString *deviceId = [[KeychainManager sharedInstance] getDeviceId];

    // 调用APIClient执行登录
    [self.apiClient loginWithDeviceId:deviceId completion:^(BOOL success, id _Nullable data, NSError * _Nullable error) {
        if (self.isCancelled) return;

        if (success && data) {
            // 保存认证数据
            [self.globalDataManager setAuthData:data];
            [self proceedToLoadStrategy];
        } else {
            // 处理登录失败
            NSLog(@"用户认证失败: %@", error.localizedDescription);

            [self transitionToState:InitializationStateFailed
                      withUserInfo:@{@"error": error ?: [NSError errorWithDomain:@"InitializationError"
                                                                           code:-1003
                                                                       userInfo:@{NSLocalizedDescriptionKey: @"User authentication failed"}]}];

            if (self.completionHandler) {
                self.completionHandler(NO, error);
            }
        }
    }];
}

- (void)proceedToLoadStrategy {
    if (self.isCancelled) return;
    
    // 步骤5: 加载策略数据
    [self transitionToState:InitializationStateLoadingStrategy withUserInfo:nil];
    
    // 检查缓存
    if (self.globalDataManager.strategyData) {
        // 有缓存，直接验证
        [self validateStrategyData];
    } else {
        // 无缓存，加载策略
        [self loadStrategyFromNetwork];
    }
}

- (void)loadStrategyFromNetwork {
    // 调用APIClient获取策略
    [self.apiClient getStrategyWithCompletion:^(BOOL success, id _Nullable data, NSError * _Nullable error) {
        if (self.isCancelled) return;

        if (success && data) {
            // 保存策略数据
            [self.globalDataManager setStrategyData:data];
            [self validateStrategyData];
        } else {
            // 处理策略获取失败
            NSLog(@"获取策略数据失败: %@", error.localizedDescription);

            [self transitionToState:InitializationStateFailed
                      withUserInfo:@{@"error": error ?: [NSError errorWithDomain:@"InitializationError"
                                                                           code:-1004
                                                                       userInfo:@{NSLocalizedDescriptionKey: @"Failed to get strategy data"}]}];

            if (self.completionHandler) {
                self.completionHandler(NO, error);
            }
        }
    }];
}

- (void)validateStrategyData {
    if (self.isCancelled) return;
    
    // 步骤6: 验证策略数据
    [self transitionToState:InitializationStateValidatingStrategy withUserInfo:nil];
    
    // 检查isReviewPkg
    if ([self.globalDataManager isReviewPackage]) {
        // 延迟2秒再次检查
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (self.isCancelled) return;
            
            // 重新加载策略数据
            [self loadStrategyFromNetwork];
            
            // 再次检查
            if ([self.globalDataManager isReviewPackage]) {
                NSError *error = [NSError errorWithDomain:@"StrategyError"
                                                     code:-1003
                                                 userInfo:@{NSLocalizedDescriptionKey: @"Application is under review"}];
                [self handleInitializationError:error];
                return;
            }
            
            [self completeInitialization];
        });
    } else {
        [self completeInitialization];
    }
}

- (void)completeInitialization {
    if (self.isCancelled) return;
    
    // 初始化完成
    [self transitionToState:InitializationStateCompleted withUserInfo:nil];
    
    if (self.completionHandler) {
        self.completionHandler(YES, nil);
        self.completionHandler = nil;
    }
}

#pragma mark - 错误处理

- (void)handleInitializationError:(NSError *)error {
    [self transitionToState:InitializationStateFailed withUserInfo:@{@"error": error}];

    if (self.completionHandler) {
        self.completionHandler(NO, error);
        self.completionHandler = nil;
    }
}

#pragma mark - 状态管理

- (void)transitionToState:(InitializationState)newState withUserInfo:(NSDictionary *)userInfo {
    if (![self canTransitionToState:newState]) {
        NSLog(@"无效的状态转换: %@ -> %@", [self stateDescription:self.currentState], [self stateDescription:newState]);
        return;
    }

    InitializationState oldState = self.currentState;
    self.currentState = newState;

    NSLog(@"状态转换: %@ -> %@", [self stateDescription:oldState], [self stateDescription:newState]);

    // 通知状态变化
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.stateChangeHandler) {
            self.stateChangeHandler(newState, userInfo);
        }
    });
}

- (BOOL)canTransitionToState:(InitializationState)newState {
    switch (self.currentState) {
        case InitializationStateIdle:
            return newState == InitializationStateWaitingNetwork;

        case InitializationStateInitializingSDK:
            return newState == InitializationStateWaitingNetwork || newState == InitializationStateFailed;

        case InitializationStateWaitingNetwork:
            return newState == InitializationStateLoadingConfig || newState == InitializationStateFailed;

        case InitializationStateLoadingConfig:
            return newState == InitializationStateAuthenticating || newState == InitializationStateFailed;

        case InitializationStateAuthenticating:
            return newState == InitializationStateLoadingStrategy || newState == InitializationStateFailed;

        case InitializationStateLoadingStrategy:
            return newState == InitializationStateValidatingStrategy || newState == InitializationStateFailed;

        case InitializationStateValidatingStrategy:
            return newState == InitializationStateCompleted ||
                   newState == InitializationStateLoadingStrategy ||
                   newState == InitializationStateFailed;

        case InitializationStateCompleted:
        case InitializationStateFailed:
            return newState == InitializationStateIdle; // 允许重置

        default:
            return NO;
    }
}

- (NSString *)stateDescription:(InitializationState)state {
    switch (state) {
        case InitializationStateIdle:
            return @"空闲";
        case InitializationStateInitializingSDK:
            return @"初始化第三方SDK";
        case InitializationStateWaitingNetwork:
            return @"等待网络连接";
        case InitializationStateLoadingConfig:
            return @"加载应用配置";
        case InitializationStateAuthenticating:
            return @"用户认证";
        case InitializationStateLoadingStrategy:
            return @"加载策略数据";
        case InitializationStateValidatingStrategy:
            return @"验证策略数据";
        case InitializationStateCompleted:
            return @"初始化完成";
        case InitializationStateFailed:
            return @"初始化失败";
        default:
            return @"未知状态";
    }
}

- (void)resetInitializationState {
    self.isCancelled = NO;
    self.completionHandler = nil;
    self.thirdPartySDKInitialized = NO;
    [self transitionToState:InitializationStateIdle withUserInfo:nil];
}

@end
