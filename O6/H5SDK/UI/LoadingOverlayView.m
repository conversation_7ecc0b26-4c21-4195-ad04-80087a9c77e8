//
//  LoadingOverlayView.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "LoadingOverlayView.h"
#import "Constants.h"

@interface LoadingOverlayView ()

@property (nonatomic, strong) UIImageView *backgroundImageView;
@property (nonatomic, strong) UIActivityIndicatorView *loadingIndicator;
@property (nonatomic, strong) UIView *errorContainerView;
@property (nonatomic, strong) UILabel *errorLabel;
@property (nonatomic, strong) UIButton *retryButton;

@end

@implementation LoadingOverlayView

#pragma mark - 初始化

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupUI];
    }
    return self;
}

#pragma mark - UI设置

- (void)setupUI {
    self.backgroundColor = [UIColor whiteColor];
    
    // 背景图片
    self.backgroundImageView = [[UIImageView alloc] init];
    self.backgroundImageView.image = [UIImage imageNamed:[Constants launchImageName]];
    self.backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.backgroundImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.backgroundImageView];
    
    // Loading指示器
    self.loadingIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.loadingIndicator.color = [UIColor whiteColor];
    self.loadingIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.loadingIndicator];
    
    // 错误容器视图
    self.errorContainerView = [[UIView alloc] init];
    self.errorContainerView.backgroundColor = [UIColor clearColor];
    self.errorContainerView.translatesAutoresizingMaskIntoConstraints = NO;
    self.errorContainerView.hidden = YES;
    [self addSubview:self.errorContainerView];
    
    // 错误信息标签
    self.errorLabel = [[UILabel alloc] init];
    self.errorLabel.text = @"Page load timeout";
    self.errorLabel.font = [UIFont systemFontOfSize:16];
    self.errorLabel.textColor = [UIColor whiteColor];
    self.errorLabel.textAlignment = NSTextAlignmentCenter;
    self.errorLabel.numberOfLines = 0;
    self.errorLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.errorContainerView addSubview:self.errorLabel];
    
    // 重试按钮
    self.retryButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.retryButton setTitle:@"Retry" forState:UIControlStateNormal];
    [self.retryButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.retryButton.backgroundColor = [UIColor systemBlueColor];
    self.retryButton.layer.cornerRadius = 8;
    self.retryButton.titleLabel.font = [UIFont systemFontOfSize:16];
    self.retryButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.retryButton addTarget:self action:@selector(retryButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.errorContainerView addSubview:self.retryButton];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // 背景图片 - 全屏
        [self.backgroundImageView.topAnchor constraintEqualToAnchor:self.topAnchor],
        [self.backgroundImageView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.backgroundImageView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
        [self.backgroundImageView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor],
        
        // Loading指示器 - 底部居中
        [self.loadingIndicator.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.loadingIndicator.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor constant:-50],
        
        // 错误容器视图 - 底部居中
        [self.errorContainerView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.errorContainerView.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor constant:-80],
        [self.errorContainerView.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.leadingAnchor constant:40],
        [self.errorContainerView.trailingAnchor constraintLessThanOrEqualToAnchor:self.trailingAnchor constant:-40],
        
        // 错误标签
        [self.errorLabel.topAnchor constraintEqualToAnchor:self.errorContainerView.topAnchor],
        [self.errorLabel.leadingAnchor constraintEqualToAnchor:self.errorContainerView.leadingAnchor],
        [self.errorLabel.trailingAnchor constraintEqualToAnchor:self.errorContainerView.trailingAnchor],
        
        // 重试按钮
        [self.retryButton.topAnchor constraintEqualToAnchor:self.errorLabel.bottomAnchor constant:20],
        [self.retryButton.centerXAnchor constraintEqualToAnchor:self.errorContainerView.centerXAnchor],
        [self.retryButton.bottomAnchor constraintEqualToAnchor:self.errorContainerView.bottomAnchor],
        [self.retryButton.widthAnchor constraintEqualToConstant:120],
        [self.retryButton.heightAnchor constraintEqualToConstant:44]
    ]];
}

#pragma mark - 公开方法

- (void)showLoading {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.hidden = NO;
        self.loadingIndicator.hidden = NO;
        self.errorContainerView.hidden = YES;
        [self.loadingIndicator startAnimating];
    });
}

- (void)showError:(NSString *)errorMessage {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.hidden = NO;
        self.loadingIndicator.hidden = YES;
        self.errorContainerView.hidden = NO;
        [self.loadingIndicator stopAnimating];
        
        if (errorMessage && errorMessage.length > 0) {
            self.errorLabel.text = errorMessage;
        } else {
            self.errorLabel.text = @"Page load timeout";
        }
    });
}

- (void)hideLoading {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.hidden = YES;
        [self.loadingIndicator stopAnimating];
    });
}

#pragma mark - 私有方法

- (void)retryButtonTapped:(UIButton *)sender {
    if (self.retryHandler) {
        self.retryHandler();
    }
}

@end
