//
//  H5SDKExample.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "H5SDKExample.h"
#import "H5SDK.h"
#import "Logger.h"
#import "Constants.h"

@interface H5SDKExampleViewController ()

@property (nonatomic, strong) UIButton *initializeButton;
@property (nonatomic, strong) UIButton *loginButton;
@property (nonatomic, strong) UIButton *payButton;
@property (nonatomic, strong) UIButton *restoreButton;
@property (nonatomic, strong) UIButton *clearButton;
@property (nonatomic, strong) UITextView *logTextView;

@end

@implementation H5SDKExampleViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    self.title = @"H5SDK示例";
    
    [self setupUI];
    
    // 开启调试模式
    [H5SDK setDebugMode:YES];
    
    // 设置日志级别
    [Logger sharedInstance].logLevel = LogLevelDebug;
    [Logger sharedInstance].fileLoggingEnabled = YES;
    
    LogI(@"H5SDK示例应用启动，SDK版本: %@", [H5SDK version]);
}

- (void)setupUI {
    // 初始化按钮
    self.initializeButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.initializeButton setTitle:@"初始化SDK" forState:UIControlStateNormal];
    [self.initializeButton addTarget:self action:@selector(initButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.initializeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.initializeButton];
    
    // 登录按钮
    self.loginButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.loginButton setTitle:@"显示登录页面" forState:UIControlStateNormal];
    [self.loginButton addTarget:self action:@selector(loginButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.loginButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.loginButton];
    
    // 支付按钮
    self.payButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.payButton setTitle:@"购买商品" forState:UIControlStateNormal];
    [self.payButton addTarget:self action:@selector(payButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.payButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.payButton];
    
    // 恢复购买按钮
    self.restoreButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.restoreButton setTitle:@"恢复购买" forState:UIControlStateNormal];
    [self.restoreButton addTarget:self action:@selector(restoreButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.restoreButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.restoreButton];
    
    // 清理按钮
    self.clearButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.clearButton setTitle:@"清理数据" forState:UIControlStateNormal];
    [self.clearButton addTarget:self action:@selector(clearButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.clearButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.clearButton];
    
    // 日志文本视图
    self.logTextView = [[UITextView alloc] init];
    self.logTextView.editable = NO;
    self.logTextView.font = [UIFont systemFontOfSize:12];
    self.logTextView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.logTextView];
    
    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [self.initializeButton.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [self.initializeButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.initializeButton.widthAnchor constraintEqualToConstant:200],
        [self.initializeButton.heightAnchor constraintEqualToConstant:40],

        [self.loginButton.topAnchor constraintEqualToAnchor:self.initializeButton.bottomAnchor constant:10],
        [self.loginButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.loginButton.widthAnchor constraintEqualToConstant:200],
        [self.loginButton.heightAnchor constraintEqualToConstant:40],
        
        [self.payButton.topAnchor constraintEqualToAnchor:self.loginButton.bottomAnchor constant:10],
        [self.payButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.payButton.widthAnchor constraintEqualToConstant:200],
        [self.payButton.heightAnchor constraintEqualToConstant:40],
        
        [self.restoreButton.topAnchor constraintEqualToAnchor:self.payButton.bottomAnchor constant:10],
        [self.restoreButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.restoreButton.widthAnchor constraintEqualToConstant:200],
        [self.restoreButton.heightAnchor constraintEqualToConstant:40],
        
        [self.clearButton.topAnchor constraintEqualToAnchor:self.restoreButton.bottomAnchor constant:10],
        [self.clearButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.clearButton.widthAnchor constraintEqualToConstant:200],
        [self.clearButton.heightAnchor constraintEqualToConstant:40],
        
        [self.logTextView.topAnchor constraintEqualToAnchor:self.clearButton.bottomAnchor constant:20],
        [self.logTextView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.logTextView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.logTextView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-20]
    ]];
    
    // 初始状态
    [self updateButtonStates];
}

- (void)updateButtonStates {
    BOOL isInitialized = [H5SDK isInitialized];
    BOOL isLoggedIn = [H5SDK isLoggedIn];
    
    self.initializeButton.enabled = !isInitialized;
    self.loginButton.enabled = isInitialized && !isLoggedIn;
    self.payButton.enabled = isInitialized && isLoggedIn;
    self.restoreButton.enabled = isInitialized;
    
    [self appendLog:[NSString stringWithFormat:@"SDK状态: 初始化=%@, 登录=%@", 
                     isInitialized ? @"是" : @"否", 
                     isLoggedIn ? @"是" : @"否"]];
}

- (void)appendLog:(NSString *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSString *timestamp = [NSDateFormatter localizedStringFromDate:[NSDate date] 
                                                             dateStyle:NSDateFormatterNoStyle 
                                                             timeStyle:NSDateFormatterMediumStyle];
        NSString *logEntry = [NSString stringWithFormat:@"[%@] %@\n", timestamp, message];
        self.logTextView.text = [self.logTextView.text stringByAppendingString:logEntry];
        
        // 滚动到底部
        NSRange range = NSMakeRange(self.logTextView.text.length - 1, 1);
        [self.logTextView scrollRangeToVisible:range];
    });
}

#pragma mark - 按钮事件

- (void)initButtonTapped {
    [self appendLog:@"正在初始化SDK..."];
    
    [H5SDK initializeWithCompletion:^(BOOL success, NSError *error) {
        if (success) {
            [self appendLog:@"SDK初始化成功"];
        } else {
            [self appendLog:[NSString stringWithFormat:@"SDK初始化失败: %@", error.localizedDescription]];
        }
        [self updateButtonStates];
    }];
}

- (void)loginButtonTapped {
    [self appendLog:@"正在显示登录页面..."];
    
    [H5SDK showLoginPageFromViewController:self];
}

- (void)payButtonTapped {
    [self appendLog:@"正在发起支付..."];
    
    [H5SDK purchaseProduct:[Constants exampleProductId]
                    source:@"shop" 
                     entry:@"main" 
                completion:^(BOOL success, NSError *error) {
        if (success) {
            [self appendLog:@"支付成功"];
        } else {
            [self appendLog:[NSString stringWithFormat:@"支付失败: %@", error.localizedDescription]];
        }
    }];
}

- (void)restoreButtonTapped {
    [self appendLog:@"正在恢复购买..."];
    
    [H5SDK restorePurchasesWithCompletion:^(BOOL success, NSError *error) {
        if (success) {
            [self appendLog:@"恢复购买成功"];
        } else {
            [self appendLog:[NSString stringWithFormat:@"恢复购买失败: %@", error.localizedDescription]];
        }
    }];
}

- (void)clearButtonTapped {
    [self appendLog:@"正在清理SDK数据..."];
    
    [H5SDK clearAllData];
    [self appendLog:@"SDK数据已清理"];
    [self updateButtonStates];
}

@end
