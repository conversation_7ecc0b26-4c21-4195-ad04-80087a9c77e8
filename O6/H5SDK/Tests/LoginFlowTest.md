# 登录与初始化流程测试验证

## 测试目标
验证登录与初始化流程的相互等待逻辑是否正确实现。

## 测试场景

### 场景1.1：初始化还没有结果时，用户已经点击了登录按钮
**测试步骤：**
1. 启动LoginViewController
2. 用户勾选协议
3. 在初始化完成前，用户点击"Fast Login"按钮
4. 等待初始化完成

**预期结果：**
- 用户点击登录按钮后，`userLoginIntention`被设置为YES
- 由于`isInitializing`为YES，显示加载状态
- 初始化成功后，自动跳转到WebViewController
- 初始化失败后，自动跳转到H5SDKExampleViewController

### 场景1.2：初始化有结果，回调时，用户还没点击登录按钮
**测试步骤：**
1. 启动LoginViewController
2. 等待初始化完成（成功或失败）
3. 用户勾选协议
4. 用户点击"Fast Login"按钮

**预期结果：**
- 初始化完成后，`initializationResult`被正确设置
- 用户点击登录按钮后，立即根据`initializationResult`进行跳转
- 成功时跳转到WebViewController，失败时跳转到H5SDKExampleViewController

### 场景1.3：初始化成功时进入网页，失败时进入H5SDKExampleViewController
**测试步骤：**
1. 模拟初始化成功的情况
2. 模拟初始化失败的情况

**预期结果：**
- 初始化成功：跳转到WebViewController，显示H5页面
- 初始化失败：跳转到H5SDKExampleViewController，显示示例界面

## 关键状态验证

### 状态变量检查
- `userLoginIntention`: 记录用户是否已点击登录按钮
- `initializationResult`: 记录初始化结果（None/Success/Failure）
- `isInitializing`: 记录是否正在初始化中

### 方法调用流程
1. `fastLoginButtonTapped` -> 设置`userLoginIntention`为YES -> 调用`handleLoginFlow`
2. `handleInitializationCompleted` -> 设置`initializationResult`为Success -> 如果`userLoginIntention`为YES则调用`handleLoginFlow`
3. `handleInitializationFailed` -> 设置`initializationResult`为Failure -> 如果`userLoginIntention`为YES则调用`handleLoginFlow`
4. `handleLoginFlow` -> 根据状态决定等待或跳转

## 测试验证点
- [ ] 用户登录意图正确记录
- [ ] 初始化结果正确记录
- [ ] 相互等待逻辑正确处理
- [ ] 成功时正确跳转到WebViewController
- [ ] 失败时正确跳转到H5SDKExampleViewController
- [ ] 登录完成回调正确调用

## 协议文本点击测试

### 测试目标
验证agreementLabelTapped函数的点击检测准确性

### 测试场景

#### 场景1：点击"Terms & Conditions"链接
**测试步骤：**
1. 启动LoginViewController
2. 精确点击"Terms & Conditions"文字区域

**预期结果：**
- 只有点击"Terms & Conditions"文字时才打开服务条款页面
- 点击其他文字区域不触发任何操作

#### 场景2：点击"Privacy Policy"链接
**测试步骤：**
1. 启动LoginViewController
2. 精确点击"Privacy Policy"文字区域

**预期结果：**
- 只有点击"Privacy Policy"文字时才打开隐私政策页面
- 点击其他文字区域不触发任何操作

#### 场景3：点击非链接区域
**测试步骤：**
1. 启动LoginViewController
2. 点击协议文本中的非链接区域（如"By using our App"）

**预期结果：**
- 不触发任何页面跳转
- 不显示任何错误提示

### 技术验证点
- [ ] characterIndexAtPoint方法正确计算字符索引
- [ ] NSRange检测准确识别链接范围
- [ ] 文本对齐方式正确处理
- [ ] 多行文本布局正确计算
- [ ] 点击检测边界准确

## 协议弹窗功能测试

### 测试目标
验证协议弹窗的显示、交互和功能完整性

### 测试场景

#### 场景1：登录按钮默认可点击
**测试步骤：**
1. 启动LoginViewController
2. 不勾选协议
3. 检查登录按钮状态

**预期结果：**
- 登录按钮始终可点击（enabled=YES, alpha=1.0）
- 只有在初始化时才禁用登录按钮

#### 场景2：未勾选协议时显示弹窗
**测试步骤：**
1. 启动LoginViewController
2. 不勾选协议
3. 点击"Fast Login"按钮

**预期结果：**
- 显示协议弹窗而不是简单alert
- 弹窗包含应用图标、版本号、协议文本、按钮
- 弹窗有进入动画效果

#### 场景3：弹窗中点击"Agree and Continue"
**测试步骤：**
1. 显示协议弹窗
2. 点击"Agree and Continue"按钮

**预期结果：**
- 自动勾选协议（isAgreementAccepted=YES）
- 关闭弹窗
- 继续登录流程

#### 场景4：弹窗中点击"Cancel"
**测试步骤：**
1. 显示协议弹窗
2. 点击"Cancel"按钮

**预期结果：**
- 关闭弹窗
- 不勾选协议
- 不继续登录流程

#### 场景5：点击背景关闭弹窗
**测试步骤：**
1. 显示协议弹窗
2. 点击弹窗外的背景区域

**预期结果：**
- 关闭弹窗
- 不勾选协议
- 不继续登录流程

#### 场景6：弹窗中的协议链接点击
**测试步骤：**
1. 显示协议弹窗
2. 点击"Terms & Conditions"或"Privacy Policy"链接

**预期结果：**
- 先关闭协议弹窗
- 正确打开对应的协议页面
- 如果有导航控制器则使用push方式，否则使用模态方式
- 模态方式显示时包含关闭按钮

#### 场景7：协议页面导航处理
**测试步骤：**
1. 从弹窗中点击协议链接
2. 检查协议页面的显示方式

**预期结果：**
- 有导航控制器时：使用push方式，可以通过返回按钮回到登录页面
- 无导航控制器时：使用模态方式，显示关闭按钮，点击关闭按钮回到登录页面

### 弹窗UI验证点
- [ ] 弹窗居中显示
- [ ] 半透明背景遮罩
- [ ] 圆角卡片样式
- [ ] 应用图标正确显示
- [ ] 版本号显示"O7-2.1.5"
- [ ] 协议文本链接样式正确
- [ ] 按钮样式符合设计
- [ ] 进入和退出动画流畅

### 协议链接修复验证点
- [ ] 点击检测正确识别链接区域
- [ ] 调试日志正确输出点击信息
- [ ] 弹窗正确关闭后打开协议页面
- [ ] 导航控制器存在时使用push方式
- [ ] 导航控制器不存在时使用模态方式
- [ ] 模态方式显示时包含关闭按钮
- [ ] 协议页面URL正确传递
- [ ] 字符索引计算准确
- [ ] 链接范围检测正确

## 用户跳转标记和自动跳转功能测试

### 测试目标
验证用户跳转标记机制和冷启动自动跳转功能

### 测试场景

#### 场景1：首次使用（无跳转标记）
**测试步骤：**
1. 清理所有SDK数据
2. 冷启动应用
3. 观察UI显示

**预期结果：**
- hasNavigated标记为NO
- 登录按钮和协议栏立即显示
- 不自动跳转，等待用户点击登录按钮
- 正常的登录流程

#### 场景2：成功进入网页后的标记
**测试步骤：**
1. 完成登录流程，成功进入WebViewController
2. 检查跳转标记

**预期结果：**
- hasNavigated标记设置为YES
- 日志显示"标记用户已跳转"

#### 场景3：失败进入示例页面后的标记
**测试步骤：**
1. 模拟初始化失败，进入H5SDKExampleViewController
2. 检查跳转标记

**预期结果：**
- hasNavigated标记设置为YES
- 日志显示"标记用户已跳转"

#### 场景4：冷启动自动跳转（初始化成功）
**测试步骤：**
1. 确保hasNavigated标记为YES
2. 冷启动应用
3. 观察UI显示和自动跳转

**预期结果：**
- 日志显示"检测到跳转记录，将延迟显示登录按钮和协议栏"
- 登录按钮和协议栏初始隐藏（alpha=0.0）
- 0.5秒后日志显示"延迟显示登录按钮和协议栏"
- 登录按钮和协议栏以动画形式显示（alpha=1.0）
- 日志显示"检查用户是否已跳转过: 是"
- 日志显示"用户已跳转过，根据初始化结果自动跳转到网页界面"
- 自动跳转到WebViewController，无需点击登录按钮

#### 场景5：冷启动自动跳转（初始化失败）
**测试步骤：**
1. 确保hasNavigated标记为YES
2. 冷启动应用
3. 模拟初始化失败

**预期结果：**
- 日志显示"检测到跳转记录，将延迟显示登录按钮和协议栏"
- 登录按钮和协议栏初始隐藏（alpha=0.0）
- 0.5秒后日志显示"延迟显示登录按钮和协议栏"
- 登录按钮和协议栏以动画形式显示（alpha=1.0）
- 日志显示"检查用户是否已跳转过: 是"
- 日志显示"用户已跳转过，根据初始化结果自动跳转到示例界面"
- 自动跳转到H5SDKExampleViewController，无需点击登录按钮

#### 场景6：退出登录清理标记
**测试步骤：**
1. 在WebViewController中触发退出登录
2. 检查跳转标记

**预期结果：**
- hasNavigated标记被清除（设置为NO）
- 日志显示"退出登录时已清除用户跳转标记"
- 下次启动不会自动跳转

#### 场景7：手动清理标记
**测试步骤：**
1. 调用[H5SDK clearAllData]或[LoginViewController clearNavigationFlag]
2. 检查跳转标记

**预期结果：**
- hasNavigated标记被清除（设置为NO）
- 下次启动不会自动跳转

### 用户跳转标记验证点
- [ ] 跳转标记正确保存到UserDefaults
- [ ] 跳转标记正确从UserDefaults读取
- [ ] 布尔值标记正确工作
- [ ] 自动跳转逻辑正确执行
- [ ] 跳转标记优先级高于用户点击
- [ ] 根据初始化结果正确选择跳转目标
- [ ] 退出登录时正确清理标记
- [ ] 手动清理功能正常工作
- [ ] 多次启动标记保持一致
- [ ] 日志输出完整准确

### UI延迟显示验证点
- [ ] 有跳转记录时登录按钮初始隐藏
- [ ] 有跳转记录时协议按钮初始隐藏
- [ ] 有跳转记录时协议文本初始隐藏
- [ ] 延迟0.5秒后开始显示动画
- [ ] 显示动画持续0.3秒
- [ ] 动画完成后UI元素完全可见
- [ ] 无跳转记录时UI立即显示
- [ ] 背景图片和加载指示器不受影响
