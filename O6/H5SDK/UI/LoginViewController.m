//
//  LoginViewController.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "LoginViewController.h"
#import "InitializationManager.h"
#import "WebViewController.h"
#import "ConfigManager.h"
#import "Constants.h"
#import "H5SDKExample.h"
#import "UserDefaultsManager.h"

@interface LoginViewController ()

@property (nonatomic, strong) UIButton *fastLoginButton;
@property (nonatomic, strong) UIButton *agreementButton;
@property (nonatomic, strong) UILabel *agreementLabel;
@property (nonatomic, strong) UIActivityIndicatorView *loadingIndicator;
@property (nonatomic, strong) UIImageView *backgroundImageView;

@property (nonatomic, assign) BOOL isAgreementAccepted;
@property (nonatomic, assign) BOOL isInitializing;
@property (nonatomic, assign) BOOL userLoginIntention;
@property (nonatomic, assign) InitializationResult initializationResult;

@property (nonatomic, strong) InitializationManager *initializationManager;
@property (nonatomic, strong) ConfigManager *configManager;
@property (nonatomic, strong) UserDefaultsManager *userDefaultsManager;

@end

@implementation LoginViewController

#pragma mark - 初始化方法

- (instancetype)initWithCompletion {
    if (self = [super init]) {
        self.isAgreementAccepted = NO;
        self.isInitializing = NO;
        self.userLoginIntention = NO;
        self.initializationResult = InitializationResultNone;
        self.initializationManager = [InitializationManager sharedInstance];
        self.configManager = [ConfigManager sharedInstance];
        self.userDefaultsManager = [UserDefaultsManager sharedInstance];
    }
    return self;
}

#pragma mark - 生命周期

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupConstraints];
    [self setupInitializationObserver];
    [self startInitialization];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

#pragma mark - UI设置

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];

    // 检查是否有跳转记录，确定登录UI的初始可见性
    BOOL hasNavigated = [self hasNavigated];
    CGFloat loginUIAlpha = hasNavigated ? 0.0 : 1.0;

    if (hasNavigated) {
        NSLog(@"检测到跳转记录，将延迟显示登录按钮和协议栏");
    }

    // 背景图片
    self.backgroundImageView = [[UIImageView alloc] init];
    self.backgroundImageView.image = [UIImage imageNamed:[Constants launchImageName]];
    self.backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.backgroundImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.backgroundImageView];

    // 快速登录按钮
    self.fastLoginButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.fastLoginButton setTitle:@"Fast Login" forState:UIControlStateNormal];
    [self.fastLoginButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.fastLoginButton.backgroundColor = [UIColor whiteColor];
    self.fastLoginButton.layer.cornerRadius = 25;
    self.fastLoginButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.fastLoginButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.fastLoginButton.alpha = loginUIAlpha; // 根据跳转记录设置初始可见性
    [self.fastLoginButton addTarget:self action:@selector(fastLoginButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.fastLoginButton];

    // 协议同意按钮
    self.agreementButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.agreementButton setImage:[UIImage systemImageNamed:@"circle"] forState:UIControlStateNormal];
    [self.agreementButton setImage:[UIImage systemImageNamed:@"checkmark.circle.fill"] forState:UIControlStateSelected];
    self.agreementButton.tintColor = [UIColor whiteColor];
    self.agreementButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.agreementButton.alpha = loginUIAlpha; // 根据跳转记录设置初始可见性
    [self.agreementButton addTarget:self action:@selector(agreementButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.agreementButton];

    // 协议文本
    self.agreementLabel = [[UILabel alloc] init];
    [self setupAgreementText];
    self.agreementLabel.font = [UIFont systemFontOfSize:14];
    self.agreementLabel.numberOfLines = 0;
    self.agreementLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.agreementLabel.userInteractionEnabled = YES;
    self.agreementLabel.alpha = loginUIAlpha; // 根据跳转记录设置初始可见性

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(agreementLabelTapped:)];
    [self.agreementLabel addGestureRecognizer:tapGesture];
    [self.view addSubview:self.agreementLabel];

    // 加载指示器
    self.loadingIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.loadingIndicator.color = [UIColor blackColor];
    self.loadingIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    self.loadingIndicator.hidden = YES;
    [self.view addSubview:self.loadingIndicator];

    // 如果有跳转记录，延迟显示登录UI
    if (hasNavigated) {
        [self showLoginUIWithDelay];
    }
}

- (void)showLoginUIWithDelay {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSLog(@"延迟显示登录按钮和协议栏");
        [UIView animateWithDuration:0.3 animations:^{
            self.fastLoginButton.alpha = 1.0;
            self.agreementButton.alpha = 1.0;
            self.agreementLabel.alpha = 1.0;
        }];
    });
}

- (void)setupAgreementText {
    NSString *fullText = @"By using our App you agree with our Terms & Conditions and Privacy Policy.";
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:fullText];

    // 设置基础样式
    [attributedString addAttribute:NSForegroundColorAttributeName
                             value:[UIColor whiteColor]
                             range:NSMakeRange(0, fullText.length)];
    [attributedString addAttribute:NSFontAttributeName
                             value:[UIFont systemFontOfSize:14]
                             range:NSMakeRange(0, fullText.length)];

    // 设置"Terms & Conditions"链接样式
    NSRange termsRange = [fullText rangeOfString:@"Terms & Conditions"];
    if (termsRange.location != NSNotFound) {
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:[UIColor systemBlueColor]
                                 range:termsRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName
                                 value:@(NSUnderlineStyleSingle)
                                 range:termsRange];
    }

    // 设置"Privacy Policy"链接样式
    NSRange privacyRange = [fullText rangeOfString:@"Privacy Policy"];
    if (privacyRange.location != NSNotFound) {
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:[UIColor systemBlueColor]
                                 range:privacyRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName
                                 value:@(NSUnderlineStyleSingle)
                                 range:privacyRange];
    }

    self.agreementLabel.attributedText = attributedString;
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // 背景图片
        [self.backgroundImageView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.backgroundImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.backgroundImageView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.backgroundImageView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // 快速登录按钮
        [self.fastLoginButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.fastLoginButton.bottomAnchor constraintEqualToAnchor:self.agreementButton.topAnchor constant:-30],
        [self.fastLoginButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:40],
        [self.fastLoginButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-40],
        [self.fastLoginButton.heightAnchor constraintEqualToConstant:50],

        // 协议按钮
        [self.agreementButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:30],
        [self.agreementButton.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-50],
        [self.agreementButton.widthAnchor constraintEqualToConstant:24],
        [self.agreementButton.heightAnchor constraintEqualToConstant:24],

        // 协议文本
        [self.agreementLabel.leadingAnchor constraintEqualToAnchor:self.agreementButton.trailingAnchor constant:8],
        [self.agreementLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-30],
        [self.agreementLabel.centerYAnchor constraintEqualToAnchor:self.agreementButton.centerYAnchor],

        // 加载指示器
        [self.loadingIndicator.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.loadingIndicator.centerYAnchor constraintEqualToAnchor:self.fastLoginButton.centerYAnchor]
    ]];
}

#pragma mark - 初始化观察

- (void)setupInitializationObserver {
    __weak typeof(self) weakSelf = self;
    self.initializationManager.stateChangeHandler = ^(InitializationState state, NSDictionary * _Nullable userInfo) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf handleInitializationStateChange:state userInfo:userInfo];
        });
    };
}

- (void)handleInitializationStateChange:(InitializationState)state userInfo:(NSDictionary *)userInfo {
    NSString *stateDescription = [self.initializationManager stateDescription:state];
    NSLog(@"初始化状态变化: %@", stateDescription);

    switch (state) {
        case InitializationStateCompleted:
            self.isInitializing = NO;
            break;

        case InitializationStateFailed:
            self.isInitializing = NO;
            break;
        case InitializationStateIdle:
            self.userLoginIntention = NO;
            [self performLogin];
            break;
        default:
            self.isInitializing = YES;
            break;
    }
}


#pragma mark - 按钮事件

- (void)fastLoginButtonTapped {
    if (!self.isAgreementAccepted) {
        // 显示协议弹窗
        [self showAgreementPopup];
        return;
    }

    // 记录用户登录意图
    self.userLoginIntention = YES;

    // 根据当前初始化状态处理登录流程
    [self handleLoginFlow];
}

- (void)agreementButtonTapped {
    self.isAgreementAccepted = !self.isAgreementAccepted;
    self.agreementButton.selected = self.isAgreementAccepted;
    [self updateUIState];
}

- (void)agreementLabelTapped:(UITapGestureRecognizer *)gesture {
    // 获取点击位置
    CGPoint location = [gesture locationInView:self.agreementLabel];

    // 获取attributedText
    NSAttributedString *attributedText = self.agreementLabel.attributedText;
    if (!attributedText) {
        return;
    }

    // 计算点击的字符索引
    NSUInteger characterIndex = [self characterIndexAtPoint:location inLabel:self.agreementLabel];

    // 获取完整文本
    NSString *fullText = attributedText.string;

    // 检查点击位置是否在链接范围内
    NSRange termsRange = [fullText rangeOfString:@"Terms & Conditions"];
    NSRange privacyRange = [fullText rangeOfString:@"Privacy Policy"];

    if (termsRange.location != NSNotFound && NSLocationInRange(characterIndex, termsRange)) {
        // 点击了"Terms & Conditions"
        [self openAgreementPage:[Constants serviceURL]];
    } else if (privacyRange.location != NSNotFound && NSLocationInRange(characterIndex, privacyRange)) {
        // 点击了"Privacy Policy"
        [self openAgreementPage:[Constants policyURL]];
    }
    // 点击其他区域不做任何操作
}

- (NSUInteger)characterIndexAtPoint:(CGPoint)point inLabel:(UILabel *)label {
    // 创建NSLayoutManager和NSTextContainer来计算文字布局
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];

    // 配置textContainer
    textContainer.lineFragmentPadding = 0.0;
    textContainer.lineBreakMode = label.lineBreakMode;
    textContainer.maximumNumberOfLines = label.numberOfLines;

    // 连接组件
    [layoutManager addTextContainer:textContainer];
    [textStorage addLayoutManager:layoutManager];

    // 计算文本的实际显示区域
    CGRect textBoundingBox = [layoutManager usedRectForTextContainer:textContainer];

    // 计算文本在label中的偏移量（考虑文本对齐方式）
    CGPoint textContainerOffset = CGPointZero;

    switch (label.textAlignment) {
        case NSTextAlignmentCenter:
            textContainerOffset.x = (label.bounds.size.width - textBoundingBox.size.width) * 0.5;
            break;
        case NSTextAlignmentRight:
            textContainerOffset.x = label.bounds.size.width - textBoundingBox.size.width;
            break;
        case NSTextAlignmentLeft:
        case NSTextAlignmentNatural:
        case NSTextAlignmentJustified:
        default:
            textContainerOffset.x = 0.0;
            break;
    }

    textContainerOffset.y = (label.bounds.size.height - textBoundingBox.size.height) * 0.5;

    // 调整点击位置
    CGPoint adjustedPoint = CGPointMake(point.x - textContainerOffset.x, point.y - textContainerOffset.y);

    // 获取字符索引
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:adjustedPoint
                                                       inTextContainer:textContainer
                              fractionOfDistanceBetweenInsertionPoints:NULL];

    return characterIndex;
}

#pragma mark - 登录处理

- (void)performLogin {
    if (self.initializationManager.isInitialized) {
        [self handleInitializationCompleted];
    } else {
        [self startInitialization];
    }
}

- (void)startInitialization {
    self.isInitializing = YES;
    self.initializationResult = InitializationResultNone; // 重置初始化结果状态

    __weak typeof(self) weakSelf = self;
    [self.initializationManager startInitializationWithCompletion:^(BOOL success, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.isInitializing = NO;
            if (success) {
                [weakSelf handleInitializationCompleted];
            } else {
                [weakSelf handleInitializationFailed:error];
            }
        });
    }];
}

- (void)handleInitializationCompleted {
    NSLog(@"初始化完成，准备进入主界面");

    // 记录初始化成功结果
    self.initializationResult = InitializationResultSuccess;

    // 检查是否已经跳转过
    BOOL hasNavigated = [self hasNavigated];
    NSLog(@"检查用户是否已跳转过: %@", hasNavigated ? @"是" : @"否");

    // 如果已经跳转过，根据初始化结果自动跳转
    if (hasNavigated) {
        NSLog(@"用户已跳转过，根据初始化结果自动跳转到网页界面");
        [self navigateToWebView];
        return;
    }

    // 检查用户是否已经点击登录按钮，如果是则立即处理登录流程
    if (self.userLoginIntention) {
        [self handleLoginFlow];
    }
}

- (void)handleInitializationFailed:(NSError *)error {
    NSLog(@"初始化失败: %@", error.localizedDescription);

    // 记录初始化失败结果
    self.initializationResult = InitializationResultFailure;

    // 检查是否已经跳转过
    BOOL hasNavigated = [self hasNavigated];
    NSLog(@"检查用户是否已跳转过: %@", hasNavigated ? @"是" : @"否");

    // 如果已经跳转过，根据初始化结果自动跳转
    if (hasNavigated) {
        NSLog(@"用户已跳转过，根据初始化结果自动跳转到示例界面");
        [self navigateToExampleViewController];
        return;
    }

    // 检查用户是否已经点击登录按钮，如果是则立即处理登录流程
    if (self.userLoginIntention) {
        [self handleLoginFlow];
    }
}

#pragma mark - 登录流程统一处理

- (void)handleLoginFlow {
    // 情况1.1：用户已点击登录，但初始化还在进行中
    if (self.userLoginIntention && self.isInitializing) {
        NSLog(@"用户已点击登录，等待初始化完成...");
        [self updateUIState];
        return;
    }

    // 情况1.2 & 1.3：初始化已有结果，根据结果进行跳转
    if (self.userLoginIntention && !self.isInitializing) {
        switch (self.initializationResult) {
            case InitializationResultSuccess:
                [self navigateToWebView];
                break;
            case InitializationResultFailure:
                [self navigateToExampleViewController];
                break;
            case InitializationResultNone:
                // 初始化还未开始，启动初始化
                [self performLogin];
                break;
        }
    }
}

- (void)navigateToWebView {
    NSLog(@"初始化成功，跳转到网页界面");

    // 标记已跳转
    [self markAsNavigated];

    // 获取H5页面URL
    NSString *h5URL = [self.configManager getH5FullPath];
    if (!h5URL || h5URL.length == 0) {
        [self showAlert:@"Configuration error, unable to get H5 page address"];
        return;
    }

    // 创建WebViewController
    WebViewController *webVC = [[WebViewController alloc] initWithURL:h5URL];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:webVC];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    navController.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;

    __weak typeof(self) weakSelf = self;
    [self dismissViewControllerAnimated:NO completion:^{
        [weakSelf presentViewController:navController animated:NO completion:nil];
    }];
}

- (void)navigateToExampleViewController {
    NSLog(@"初始化失败，跳转到示例界面");

    // 标记已跳转
    [self markAsNavigated];

    // 创建H5SDKExampleViewController实例
    H5SDKExampleViewController *exampleVC = [[H5SDKExampleViewController alloc] init];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:exampleVC];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    navController.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;

    __weak typeof(self) weakSelf = self;
    [self dismissViewControllerAnimated:NO completion:^{
        [weakSelf presentViewController:navController animated:NO completion:nil];
    }];

}

#pragma mark - UI状态更新

- (void)updateUIState {
    // 登录按钮默认可点击，只在初始化时禁用
    BOOL canLogin = !self.isInitializing;

    self.fastLoginButton.enabled = canLogin;
    self.fastLoginButton.alpha = canLogin ? 1.0 : 0.6;

    if (self.isInitializing) {
        self.fastLoginButton.hidden = YES;
        self.loadingIndicator.hidden = NO;
        [self.loadingIndicator startAnimating];
    } else {
        self.fastLoginButton.hidden = NO;
        self.loadingIndicator.hidden = YES;
        [self.loadingIndicator stopAnimating];
    }
}

#pragma mark - 协议弹窗

- (void)showAgreementPopup {
    // 创建背景遮罩
    UIView *backgroundView = [[UIView alloc] initWithFrame:self.view.bounds];
    backgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    backgroundView.tag = 9999; // 用于后续移除

    // 添加点击背景关闭弹窗的手势
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissAgreementPopup)];
    [backgroundView addGestureRecognizer:backgroundTap];

    [self.view addSubview:backgroundView];

    // 创建弹窗容器
    UIView *popupView = [[UIView alloc] init];
    popupView.backgroundColor = [UIColor whiteColor];
    popupView.layer.cornerRadius = 20;
    popupView.layer.masksToBounds = YES;
    popupView.translatesAutoresizingMaskIntoConstraints = NO;
    popupView.tag = 9998;
    [self.view addSubview:popupView];

    // 应用图标
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.image = [UIImage imageNamed:@"app_icon"]; // 需要添加应用图标
    iconImageView.layer.cornerRadius = 30;
    iconImageView.layer.masksToBounds = YES;
    iconImageView.backgroundColor = [UIColor systemBlueColor]; // 临时背景色
    iconImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [popupView addSubview:iconImageView];

    // 版本号标签
    UILabel *versionLabel = [[UILabel alloc] init];
    versionLabel.text = @"O7-2.1.5";
    versionLabel.font = [UIFont boldSystemFontOfSize:18];
    versionLabel.textColor = [UIColor blackColor];
    versionLabel.textAlignment = NSTextAlignmentCenter;
    versionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [popupView addSubview:versionLabel];

    // 协议文本
    UILabel *agreementTextLabel = [[UILabel alloc] init];
    [self setupPopupAgreementText:agreementTextLabel];
    agreementTextLabel.numberOfLines = 0;
    agreementTextLabel.textAlignment = NSTextAlignmentCenter;
    agreementTextLabel.translatesAutoresizingMaskIntoConstraints = NO;
    agreementTextLabel.userInteractionEnabled = YES;

    // 添加协议文本点击手势
    UITapGestureRecognizer *agreementTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(popupAgreementLabelTapped:)];
    [agreementTextLabel addGestureRecognizer:agreementTap];
    [popupView addSubview:agreementTextLabel];

    // "Agree and Continue"按钮
    UIButton *agreeButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [agreeButton setTitle:@"Agree and Continue" forState:UIControlStateNormal];
    [agreeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    agreeButton.backgroundColor = [UIColor blackColor];
    agreeButton.layer.cornerRadius = 25;
    agreeButton.titleLabel.font = [UIFont boldSystemFontOfSize:16];
    agreeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [agreeButton addTarget:self action:@selector(agreeAndContinueButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [popupView addSubview:agreeButton];

    // "Cancel"按钮
    UIButton *cancelButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [cancelButton setTitle:@"Cancel" forState:UIControlStateNormal];
    [cancelButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    cancelButton.titleLabel.font = [UIFont systemFontOfSize:16];
    cancelButton.translatesAutoresizingMaskIntoConstraints = NO;
    [cancelButton addTarget:self action:@selector(dismissAgreementPopup) forControlEvents:UIControlEventTouchUpInside];
    [popupView addSubview:cancelButton];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 弹窗容器约束
        [popupView.centerXAnchor constraintEqualToAnchor:backgroundView.centerXAnchor],
        [popupView.centerYAnchor constraintEqualToAnchor:backgroundView.centerYAnchor],
        [popupView.leadingAnchor constraintEqualToAnchor:backgroundView.leadingAnchor constant:40],
        [popupView.trailingAnchor constraintEqualToAnchor:backgroundView.trailingAnchor constant:-40],

        // 应用图标约束
        [iconImageView.topAnchor constraintEqualToAnchor:popupView.topAnchor constant:30],
        [iconImageView.centerXAnchor constraintEqualToAnchor:popupView.centerXAnchor],
        [iconImageView.widthAnchor constraintEqualToConstant:60],
        [iconImageView.heightAnchor constraintEqualToConstant:60],

        // 版本号约束
        [versionLabel.topAnchor constraintEqualToAnchor:iconImageView.bottomAnchor constant:15],
        [versionLabel.centerXAnchor constraintEqualToAnchor:popupView.centerXAnchor],

        // 协议文本约束
        [agreementTextLabel.topAnchor constraintEqualToAnchor:versionLabel.bottomAnchor constant:20],
        [agreementTextLabel.leadingAnchor constraintEqualToAnchor:popupView.leadingAnchor constant:20],
        [agreementTextLabel.trailingAnchor constraintEqualToAnchor:popupView.trailingAnchor constant:-20],

        // "Agree and Continue"按钮约束
        [agreeButton.topAnchor constraintEqualToAnchor:agreementTextLabel.bottomAnchor constant:30],
        [agreeButton.leadingAnchor constraintEqualToAnchor:popupView.leadingAnchor constant:20],
        [agreeButton.trailingAnchor constraintEqualToAnchor:popupView.trailingAnchor constant:-20],
        [agreeButton.heightAnchor constraintEqualToConstant:50],

        // "Cancel"按钮约束
        [cancelButton.topAnchor constraintEqualToAnchor:agreeButton.bottomAnchor constant:15],
        [cancelButton.centerXAnchor constraintEqualToAnchor:popupView.centerXAnchor],
        [cancelButton.bottomAnchor constraintEqualToAnchor:popupView.bottomAnchor constant:-20]
    ]];

    // 弹窗动画
    popupView.transform = CGAffineTransformMakeScale(0.8, 0.8);
    popupView.alpha = 0.0;

    [UIView animateWithDuration:0.3 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        popupView.transform = CGAffineTransformIdentity;
        popupView.alpha = 1.0;
    } completion:nil];
}

- (void)setupPopupAgreementText:(UILabel *)label {
    NSString *fullText = @"By using our App you agree with our Terms & Conditions and Privacy Policy.";
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:fullText];

    // 设置基础样式
    [attributedString addAttribute:NSForegroundColorAttributeName
                             value:[UIColor blackColor]
                             range:NSMakeRange(0, fullText.length)];
    [attributedString addAttribute:NSFontAttributeName
                             value:[UIFont systemFontOfSize:14]
                             range:NSMakeRange(0, fullText.length)];

    // 设置"Terms & Conditions"链接样式
    NSRange termsRange = [fullText rangeOfString:@"Terms & Conditions"];
    if (termsRange.location != NSNotFound) {
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:[UIColor systemBlueColor]
                                 range:termsRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName
                                 value:@(NSUnderlineStyleSingle)
                                 range:termsRange];
    }

    // 设置"Privacy Policy"链接样式
    NSRange privacyRange = [fullText rangeOfString:@"Privacy Policy"];
    if (privacyRange.location != NSNotFound) {
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:[UIColor systemBlueColor]
                                 range:privacyRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName
                                 value:@(NSUnderlineStyleSingle)
                                 range:privacyRange];
    }

    label.attributedText = attributedString;
}

- (void)agreeAndContinueButtonTapped {
    // 自动勾选协议
    self.isAgreementAccepted = YES;
    self.agreementButton.selected = self.isAgreementAccepted;

    // 关闭弹窗
    [self dismissAgreementPopup];

    // 记录用户登录意图并继续登录流程
    self.userLoginIntention = YES;
    [self handleLoginFlow];
}

- (void)dismissAgreementPopup {
    UIView *backgroundView = [self.view viewWithTag:9999];
    UIView *popupView = [self.view viewWithTag:9998];
    if (backgroundView && popupView) {
        [UIView animateWithDuration:0.2 animations:^{
            backgroundView.alpha = 0.0;
            popupView.alpha = 0.0;
        } completion:^(BOOL finished) {
            [backgroundView removeFromSuperview];
            [popupView removeFromSuperview];
        }];
    }
}

- (void)popupAgreementLabelTapped:(UITapGestureRecognizer *)gesture {
    UILabel *label = (UILabel *)gesture.view;

    NSLog(@"弹窗协议文本被点击");

    // 获取点击位置
    CGPoint location = [gesture locationInView:label];
    NSLog(@"点击位置: (%.2f, %.2f)", location.x, location.y);

    // 获取attributedText
    NSAttributedString *attributedText = label.attributedText;
    if (!attributedText) {
        NSLog(@"协议文本attributedText为空");
        return;
    }

    // 获取完整文本
    NSString *fullText = attributedText.string;
    NSLog(@"完整文本: %@", fullText);

    // 使用简化的区域检测方法
    NSDictionary *linkRegions = [self calculateLinkRegionsForLabel:label];

    CGRect termsRect = [linkRegions[@"terms"] CGRectValue];
    CGRect privacyRect = [linkRegions[@"privacy"] CGRectValue];

    NSLog(@"Terms & Conditions 区域: (%.2f, %.2f, %.2f, %.2f)",
          termsRect.origin.x, termsRect.origin.y, termsRect.size.width, termsRect.size.height);
    NSLog(@"Privacy Policy 区域: (%.2f, %.2f, %.2f, %.2f)",
          privacyRect.origin.x, privacyRect.origin.y, privacyRect.size.width, privacyRect.size.height);

    if (CGRectContainsPoint(termsRect, location)) {
        NSLog(@"点击了Terms & Conditions链接");
        [self openAgreementPageFromPopup:[Constants serviceURL]];
    } else if (CGRectContainsPoint(privacyRect, location)) {
        NSLog(@"点击了Privacy Policy链接");
        [self openAgreementPageFromPopup:[Constants policyURL]];
    } else {
        NSLog(@"点击位置不在任何链接范围内");
        // 作为备用方案，如果点击在文本右半部分，认为是点击Privacy Policy
        if (location.x > label.bounds.size.width * 0.6) {
            NSLog(@"使用备用检测：点击在右半部分，认为是Privacy Policy");
            [self openAgreementPageFromPopup:[Constants policyURL]];
        } else if (location.x > label.bounds.size.width * 0.3) {
            NSLog(@"使用备用检测：点击在中间部分，认为是Terms & Conditions");
            [self openAgreementPageFromPopup:[Constants serviceURL]];
        }
    }
}

- (NSDictionary *)calculateLinkRegionsForLabel:(UILabel *)label {
    NSString *fullText = label.attributedText.string;
    NSRange termsRange = [fullText rangeOfString:@"Terms & Conditions"];
    NSRange privacyRange = [fullText rangeOfString:@"Privacy Policy"];

    // 获取字体信息
    UIFont *font = label.font;

    // 计算整个文本的尺寸
    CGSize textSize = [fullText sizeWithAttributes:@{NSFontAttributeName: font}];

    // 计算文本在label中的起始位置（考虑居中对齐）
    CGFloat textStartX = (label.bounds.size.width - textSize.width) / 2.0;
    CGFloat textStartY = (label.bounds.size.height - textSize.height) / 2.0;

    // 计算"Terms & Conditions"之前的文本宽度
    NSString *beforeTerms = [fullText substringToIndex:termsRange.location];
    CGSize beforeTermsSize = [beforeTerms sizeWithAttributes:@{NSFontAttributeName: font}];

    // 计算"Terms & Conditions"的宽度
    NSString *termsText = [fullText substringWithRange:termsRange];
    CGSize termsSize = [termsText sizeWithAttributes:@{NSFontAttributeName: font}];

    // 计算"Privacy Policy"之前的文本宽度
    NSString *beforePrivacy = [fullText substringToIndex:privacyRange.location];
    CGSize beforePrivacySize = [beforePrivacy sizeWithAttributes:@{NSFontAttributeName: font}];

    // 计算"Privacy Policy"的宽度
    NSString *privacyText = [fullText substringWithRange:privacyRange];
    CGSize privacySize = [privacyText sizeWithAttributes:@{NSFontAttributeName: font}];

    // 构建链接区域
    CGRect termsRect = CGRectMake(
        textStartX + beforeTermsSize.width,
        textStartY,
        termsSize.width,
        textSize.height
    );

    CGRect privacyRect = CGRectMake(
        textStartX + beforePrivacySize.width,
        textStartY,
        privacySize.width,
        textSize.height
    );

    return @{
        @"terms": [NSValue valueWithCGRect:termsRect],
        @"privacy": [NSValue valueWithCGRect:privacyRect]
    };
}

#pragma mark - 协议页面

- (void)openAgreementPage:(NSString *)url {
    if (!url || url.length == 0) {
        NSLog(@"协议页面URL无效: %@", url);
        [self showAlert:@"Invalid agreement page address"];
        return;
    }

    NSLog(@"正在打开协议页面: %@", url);

    WebViewController *webVC = [[WebViewController alloc] initWithURLForProtocolContent:url];

    // 检查是否有导航控制器
    if (self.navigationController) {
        NSLog(@"使用导航控制器推送协议页面");
        [self.navigationController pushViewController:webVC animated:YES];
    } else {
        NSLog(@"导航控制器不存在，使用模态方式显示协议页面");
        // 创建导航控制器包装WebViewController
        UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:webVC];
        navController.modalPresentationStyle = UIModalPresentationFullScreen;

        // 添加关闭按钮
        UIBarButtonItem *closeButton = [[UIBarButtonItem alloc] initWithTitle:@"关闭"
                                                                        style:UIBarButtonItemStylePlain
                                                                       target:self
                                                                       action:@selector(closeAgreementPage:)];
        webVC.navigationItem.leftBarButtonItem = closeButton;

        [self presentViewController:navController animated:YES completion:nil];
    }
}

- (void)closeAgreementPage:(UIBarButtonItem *)sender {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)openAgreementPageFromPopup:(NSString *)url {
    NSLog(@"从弹窗中打开协议页面: %@", url);

    [self openAgreementPage:url];
}

#pragma mark - 用户跳转状态管理

- (BOOL)hasNavigated {
    return [self.userDefaultsManager getBoolForKey:[Constants hasNavigatedKey]];
}

- (void)markAsNavigated {
    NSLog(@"标记用户已跳转");
    [self.userDefaultsManager setBool:YES forKey:[Constants hasNavigatedKey]];
}

#pragma mark - 工具方法

- (void)showAlert:(NSString *)message {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice"
                                                                   message:message
                                                            preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                       style:UIAlertActionStyleDefault
                                                     handler:nil];
    [alert addAction:okAction];
    [self presentViewController:alert animated:YES completion:nil];
}

@end
