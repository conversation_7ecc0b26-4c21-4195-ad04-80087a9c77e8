//
//  UserDefaultsManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "UserDefaultsManager.h"
#import "Constants.h"

// 缓存键名常量已迁移到Constants中

@interface UserDefaultsManager ()

@property (nonatomic, strong) NSUserDefaults *userDefaults;
@property (nonatomic, strong) dispatch_queue_t storageQueue;

@end

@implementation UserDefaultsManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static UserDefaultsManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.userDefaults = [NSUserDefaults standardUserDefaults];
        self.storageQueue = dispatch_queue_create([Constants storageQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

#pragma mark - 配置数据缓存

- (nullable NSDictionary *)getAppConfigData {
    __block NSDictionary *configData = nil;
    dispatch_sync(self.storageQueue, ^{
        configData = [self.userDefaults dictionaryForKey:[Constants appConfigDataKey]];
    });
    return configData;
}

- (void)setAppConfigData:(NSDictionary *)configData {
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults setObject:configData forKey:[Constants appConfigDataKey]];
        [self setDataTimestamp:[Constants appConfigDataKey]];
        [self.userDefaults synchronize];
    });
}

- (void)clearAppConfigData {
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults removeObjectForKey:[Constants appConfigDataKey]];
        [self.userDefaults removeObjectForKey:[[Constants appConfigDataKey] stringByAppendingString:[Constants timestampSuffix]]];
        [self.userDefaults synchronize];
    });
}

#pragma mark - 认证数据缓存

- (nullable NSDictionary *)getAuthData {
    __block NSDictionary *authData = nil;
    dispatch_sync(self.storageQueue, ^{
        authData = [self.userDefaults dictionaryForKey:[Constants authDataKey]];
    });
    return authData;
}

- (void)setAuthData:(NSDictionary *)authData {
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults setObject:authData forKey:[Constants authDataKey]];
        [self setDataTimestamp:[Constants authDataKey]];
        [self.userDefaults synchronize];
    });
}

- (void)clearAuthData {
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults removeObjectForKey:[Constants authDataKey]];
        [self.userDefaults removeObjectForKey:[[Constants authDataKey] stringByAppendingString:[Constants timestampSuffix]]];
        [self.userDefaults synchronize];
    });
}

#pragma mark - 策略数据缓存

- (nullable NSDictionary *)getStrategyData {
    __block NSDictionary *strategyData = nil;
    dispatch_sync(self.storageQueue, ^{
        strategyData = [self.userDefaults dictionaryForKey:[Constants strategyDataKey]];
    });
    return strategyData;
}

- (void)setStrategyData:(NSDictionary *)strategyData {
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults setObject:strategyData forKey:[Constants strategyDataKey]];
        [self setDataTimestamp:[Constants strategyDataKey]];
        [self.userDefaults synchronize];
    });
}

- (void)clearStrategyData {
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults removeObjectForKey:[Constants strategyDataKey]];
        [self.userDefaults removeObjectForKey:[[Constants strategyDataKey] stringByAppendingString:[Constants timestampSuffix]]];
        [self.userDefaults synchronize];
    });
}

#pragma mark - 通用存储方法

- (nullable id)getObjectForKey:(NSString *)key {
    if (!key) return nil;
    
    __block id object = nil;
    dispatch_sync(self.storageQueue, ^{
        object = [self.userDefaults objectForKey:key];
    });
    return object;
}

- (void)setObject:(nullable id)object forKey:(NSString *)key {
    if (!key) return;
    
    dispatch_async(self.storageQueue, ^{
        if (object) {
            [self.userDefaults setObject:object forKey:key];
        } else {
            [self.userDefaults removeObjectForKey:key];
        }
        [self.userDefaults synchronize];
    });
}

- (void)removeObjectForKey:(NSString *)key {
    if (!key) return;
    
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults removeObjectForKey:key];
        [self.userDefaults synchronize];
    });
}

#pragma mark - 类型安全的存储方法

- (nullable NSString *)getStringForKey:(NSString *)key {
    if (!key) return nil;
    
    __block NSString *string = nil;
    dispatch_sync(self.storageQueue, ^{
        string = [self.userDefaults stringForKey:key];
    });
    return string;
}

- (void)setString:(nullable NSString *)string forKey:(NSString *)key {
    [self setObject:string forKey:key];
}

- (NSInteger)getIntegerForKey:(NSString *)key {
    if (!key) return 0;
    
    __block NSInteger integer = 0;
    dispatch_sync(self.storageQueue, ^{
        integer = [self.userDefaults integerForKey:key];
    });
    return integer;
}

- (void)setInteger:(NSInteger)integer forKey:(NSString *)key {
    if (!key) return;
    
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults setInteger:integer forKey:key];
        [self.userDefaults synchronize];
    });
}

- (BOOL)getBoolForKey:(NSString *)key {
    if (!key) return NO;
    
    __block BOOL boolValue = NO;
    dispatch_sync(self.storageQueue, ^{
        boolValue = [self.userDefaults boolForKey:key];
    });
    return boolValue;
}

- (void)setBool:(BOOL)value forKey:(NSString *)key {
    if (!key) return;
    
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults setBool:value forKey:key];
        [self.userDefaults synchronize];
    });
}

- (double)getDoubleForKey:(NSString *)key {
    if (!key) return 0.0;
    
    __block double doubleValue = 0.0;
    dispatch_sync(self.storageQueue, ^{
        doubleValue = [self.userDefaults doubleForKey:key];
    });
    return doubleValue;
}

- (void)setDouble:(double)value forKey:(NSString *)key {
    if (!key) return;
    
    dispatch_async(self.storageQueue, ^{
        [self.userDefaults setDouble:value forKey:key];
        [self.userDefaults synchronize];
    });
}

#pragma mark - 数据过期检查

- (BOOL)isDataExpired:(NSString *)key maxAge:(NSTimeInterval)maxAge {
    if (!key || maxAge <= 0) return YES;

    NSString *timestampKey = [key stringByAppendingString:[Constants timestampSuffix]];
    NSTimeInterval timestamp = [self getDoubleForKey:timestampKey];

    if (timestamp == 0) return YES;

    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    return (currentTime - timestamp) > maxAge;
}

- (void)setDataTimestamp:(NSString *)key {
    if (!key) return;

    NSString *timestampKey = [key stringByAppendingString:[Constants timestampSuffix]];
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    [self setDouble:currentTime forKey:timestampKey];
}

@end
