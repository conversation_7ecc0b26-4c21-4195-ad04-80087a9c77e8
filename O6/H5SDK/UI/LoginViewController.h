//
//  LoginViewController.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

// 初始化结果枚举
typedef NS_ENUM(NSInteger, InitializationResult) {
    InitializationResultNone,       // 未初始化
    InitializationResultSuccess,    // 初始化成功
    InitializationResultFailure     // 初始化失败
};

@interface LoginViewController : UIViewController

// 初始化方法
- (instancetype)initWithCompletion;

// UI元素
@property (nonatomic, strong, readonly) UIButton *fastLoginButton;
@property (nonatomic, strong, readonly) UIButton *agreementButton;
@property (nonatomic, strong, readonly) UILabel *agreementLabel;
@property (nonatomic, strong, readonly) UIActivityIndicatorView *loadingIndicator;

// 状态管理
@property (nonatomic, assign, readonly) BOOL isAgreementAccepted;
@property (nonatomic, assign, readonly) BOOL isInitializing;
@property (nonatomic, assign, readonly) BOOL userLoginIntention;
@property (nonatomic, assign, readonly) InitializationResult initializationResult;

// 手动触发登录
- (void)performLogin;

@end

NS_ASSUME_NONNULL_END
