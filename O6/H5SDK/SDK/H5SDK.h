//
//  H5SDK.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

// SDK版本信息
FOUNDATION_EXPORT NSString * const H5SDKVersion;

// 初始化完成回调
typedef void(^H5SDKInitializationHandler)(BOOL success, NSError * _Nullable error);

// 支付完成回调
typedef void(^H5SDKPaymentHandler)(BOOL success, NSError * _Nullable error);

@interface H5SDK : NSObject

// SDK版本
+ (NSString *)version;

// 初始化SDK
+ (void)initializeWithCompletion:(H5SDKInitializationHandler)completion;

// 显示登录页面
+ (void)showLoginPageFromViewController:(UIViewController *)viewController;

// 显示主页面
+ (void)showMainPageFromViewController:(UIViewController *)viewController;

// 支付功能
+ (void)purchaseProduct:(NSString *)productCode 
                 source:(nullable NSString *)source 
                  entry:(nullable NSString *)entry 
             completion:(H5SDKPaymentHandler)completion;

// 恢复购买
+ (void)restorePurchasesWithCompletion:(H5SDKPaymentHandler)completion;

// SDK状态查询
+ (BOOL)isInitialized;
+ (BOOL)isLoggedIn;

// 获取用户信息
+ (nullable NSDictionary *)getUserInfo;

// 获取设备信息
+ (NSDictionary *)getDeviceInfo;

// 清理SDK数据
+ (void)clearAllData;

// 调试模式
+ (void)setDebugMode:(BOOL)enabled;

@end

NS_ASSUME_NONNULL_END
